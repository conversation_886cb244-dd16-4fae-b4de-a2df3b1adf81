within Workspace.Controller.StateMachine;
partial model StateMachine61AQ
  inner BOLT.Control.SteadyState.Utilities.CalculateExtraOutputs calculateExtraOutputs;
  parameter Boolean UseStateMachine=true
    "If true then StateMachine works normally if false, StateMachine stop and initiale current mode ID is never change"
    annotation (Dialog(group="Use Parameters"));
  .BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine(
    redeclare type ModeID=ModeID61AQ,
    redeclare record Mode=Mode61AQBase,
    modes={mode_OneModule_1,mode_TwoModule_2,mode_ThreeModule_3,mode_FourModule_4},
    currentModeID=Workspace.Controller.StateMachine.ModeID61AQ.OneModule_1)
    annotation (Placement(transformation(extent={{-99.16850320237154,81.41768888798414},{-81.8653080072389,98.72088408311681}},origin={0.0,0.0},rotation=0.0)));
  //    BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine;
  //    .BOLT.Control.SteadyState.StateMachine.StateMachine stateMachine(
  //             redeclare type ModeID = ModeID61AQ,
  //             redeclare record Mode = Mode61AQBase,
  //             currentModeID = ModeID61AQ.OneModule_1,
  //             modes = {mode_OneModule_1,
  //                      mode_TwoModule_2,
  //                      mode_ThreeModule_3,
  //                      mode_FourModule_4});
  // Boolean declarations
  Boolean transitTo2;
  Boolean transitTo3;
  Boolean transitTo4;
  Boolean actuators_OnOff[4];
  Boolean Module_2_ON;
  Boolean Module_3_ON;
  Boolean Module_4_ON;
  Integer Stateid;
  // Modes declarations
  OneModule_1 mode_OneModule_1(
    transitionCondition=false);
  TwoModule_2 mode_TwoModule_2(
    transitionCondition=transitTo2);
  ThreeModule_3 mode_ThreeModule_3(
    transitionCondition=transitTo3);
  FourModule_4 mode_FourModule_4(
    transitionCondition=transitTo4);
// Nonbus Variables declarations (optional)
equation
  // Optimized transition condition equations - Boolean compatible
  // Use smooth functions but convert to Boolean properly
  transitTo2 = smooth(1,
    if UseStateMachine then
      (Modelica.Math.tanh(10*(if Module_2_ON then 1 else 0) - 5) + 1)/2 *
      (Modelica.Math.tanh(10*(3 - Stateid)) + 1)/2 +
      (Modelica.Math.tanh(10*(if Stateid == 2 then 1 else 0) - 5) + 1)/2
    else
      0) > 0.5;

  transitTo3 = smooth(1,
    if UseStateMachine then
      (Modelica.Math.tanh(10*(if Module_3_ON then 1 else 0) - 5) + 1)/2 *
      (Modelica.Math.tanh(10*(4 - Stateid)) + 1)/2 +
      (Modelica.Math.tanh(10*(if Stateid == 3 then 1 else 0) - 5) + 1)/2
    else
      0) > 0.5;

  transitTo4 = smooth(1,
    if UseStateMachine then
      (Modelica.Math.tanh(10*(if Module_4_ON then 1 else 0) - 5) + 1)/2 +
      (Modelica.Math.tanh(10*(if Stateid == 4 then 1 else 0) - 5) + 1)/2
    else
      0) > 0.5;
  // Optimized actuator logic using smooth transitions to reduce Jacobian recalculations
  // Use smooth interpolation with proper Boolean conversion
protected
  Real modeWeight[4];
equation
  // Calculate smooth mode weights
  modeWeight[1] = smooth(1, (Modelica.Math.tanh(10*(if StateMachine.nextModeID == ModeID61AQ.OneModule_1 then 1 else 0) - 5) + 1)/2);
  modeWeight[2] = smooth(1, (Modelica.Math.tanh(10*(if StateMachine.nextModeID == ModeID61AQ.TwoModule_2 then 1 else 0) - 5) + 1)/2);
  modeWeight[3] = smooth(1, (Modelica.Math.tanh(10*(if StateMachine.nextModeID == ModeID61AQ.ThreeModule_3 then 1 else 0) - 5) + 1)/2);
  modeWeight[4] = smooth(1, (Modelica.Math.tanh(10*(if StateMachine.nextModeID == ModeID61AQ.FourModule_4 then 1 else 0) - 5) + 1)/2);

  // Smooth actuator assignments using weighted interpolation with Boolean conversion
  actuators_OnOff[1] = modeWeight[1]*(if mode_OneModule_1.Module_1 then 1 else 0) +
                       modeWeight[2]*(if mode_TwoModule_2.Module_1 then 1 else 0) +
                       modeWeight[3]*(if mode_ThreeModule_3.Module_1 then 1 else 0) +
                       modeWeight[4]*(if mode_FourModule_4.Module_1 then 1 else 0) > 0.5;

  actuators_OnOff[2] = modeWeight[1]*(if mode_OneModule_1.Module_2 then 1 else 0) +
                       modeWeight[2]*(if mode_TwoModule_2.Module_2 then 1 else 0) +
                       modeWeight[3]*(if mode_ThreeModule_3.Module_2 then 1 else 0) +
                       modeWeight[4]*(if mode_FourModule_4.Module_2 then 1 else 0) > 0.5;

  actuators_OnOff[3] = modeWeight[1]*(if mode_OneModule_1.Module_3 then 1 else 0) +
                       modeWeight[2]*(if mode_TwoModule_2.Module_3 then 1 else 0) +
                       modeWeight[3]*(if mode_ThreeModule_3.Module_3 then 1 else 0) +
                       modeWeight[4]*(if mode_FourModule_4.Module_3 then 1 else 0) > 0.5;

  actuators_OnOff[4] = modeWeight[1]*(if mode_OneModule_1.Module_4 then 1 else 0) +
                       modeWeight[2]*(if mode_TwoModule_2.Module_4 then 1 else 0) +
                       modeWeight[3]*(if mode_ThreeModule_3.Module_4 then 1 else 0) +
                       modeWeight[4]*(if mode_FourModule_4.Module_4 then 1 else 0) > 0.5;

end StateMachine61AQ;
