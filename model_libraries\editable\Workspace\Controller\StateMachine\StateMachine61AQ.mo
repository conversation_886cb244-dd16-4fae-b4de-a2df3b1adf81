within Workspace.Controller.StateMachine;
partial model StateMachine61AQ
  inner BOLT.Control.SteadyState.Utilities.CalculateExtraOutputs calculateExtraOutputs;
  parameter Boolean UseStateMachine=true
    "If true then StateMachine works normally if false, StateMachine stop and initiale current mode ID is never change"
    annotation (Dialog(group="Use Parameters"));
  .BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine(
    redeclare type ModeID=ModeID61AQ,
    redeclare record Mode=Mode61AQBase,
    modes={mode_OneModule_1,mode_TwoModule_2,mode_ThreeModule_3,mode_FourModule_4},
    currentModeID=Workspace.Controller.StateMachine.ModeID61AQ.OneModule_1)
    annotation (Placement(transformation(extent={{-99.16850320237154,81.41768888798414},{-81.8653080072389,98.72088408311681}},origin={0.0,0.0},rotation=0.0)));
  //    BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine;
  //    .BOLT.Control.SteadyState.StateMachine.StateMachine stateMachine(
  //             redeclare type ModeID = ModeID61AQ,
  //             redeclare record Mode = Mode61AQBase,
  //             currentModeID = ModeID61AQ.OneModule_1,
  //             modes = {mode_OneModule_1,
  //                      mode_TwoModule_2,
  //                      mode_ThreeModule_3,
  //                      mode_FourModule_4});
  // Boolean declarations
  Boolean transitTo2;
  Boolean transitTo3;
  Boolean transitTo4;
  Boolean actuators_OnOff[4];
  Boolean Module_2_ON;
  Boolean Module_3_ON;
  Boolean Module_4_ON;
  Integer Stateid;
  // Modes declarations
  OneModule_1 mode_OneModule_1(
    transitionCondition=false);
  TwoModule_2 mode_TwoModule_2(
    transitionCondition=transitTo2);
  ThreeModule_3 mode_ThreeModule_3(
    transitionCondition=transitTo3);
  FourModule_4 mode_FourModule_4(
    transitionCondition=transitTo4);
// Nonbus Variables declarations (optional)
equation
  // Transition condition equations
  when calculateExtraOutputs then
    transitTo2=(
      if UseStateMachine then
        (Module_2_ON and Stateid < 3) or(Stateid == 2)
      else
        false);
    transitTo3=(
      if UseStateMachine then
        (Module_3_ON and Stateid < 4) or(Stateid == 3)
      else
        false);
    transitTo4=(
      if UseStateMachine then
        Module_4_ON or(Stateid == 4)
      else
        false);
  end when;
  // Use conditional expressions instead of variable array indexing to improve simulation performance
actuators_OnOff={ 
    if StateMachine.nextModeID == ModeID61AQ.OneModule_1 then mode_OneModule_1.Module_1
    elseif StateMachine.nextModeID == ModeID61AQ.TwoModule_2 then mode_TwoModule_2.Module_1
    elseif StateMachine.nextModeID == ModeID61AQ.ThreeModule_3 then mode_ThreeModule_3.Module_1
    else mode_FourModule_4.Module_1,

    if StateMachine.nextModeID == ModeID61AQ.OneModule_1 then mode_OneModule_1.Module_2
    elseif StateMachine.nextModeID == ModeID61AQ.TwoModule_2 then mode_TwoModule_2.Module_2
    elseif StateMachine.nextModeID == ModeID61AQ.ThreeModule_3 then mode_ThreeModule_3.Module_2
    else mode_FourModule_4.Module_2,

    if StateMachine.nextModeID == ModeID61AQ.OneModule_1 then mode_OneModule_1.Module_3
    else if StateMachine.nextModeID == ModeID61AQ.TwoModule_2 then mode_TwoModule_2.Module_3
    else if StateMachine.nextModeID == ModeID61AQ.ThreeModule_3 then mode_ThreeModule_3.Module_3
    else mode_FourModule_4.Module_3,

    if StateMachine.nextModeID == ModeID61AQ.OneModule_1 then mode_OneModule_1.Module_4
    else if StateMachine.nextModeID == ModeID61AQ.TwoModule_2 then mode_TwoModule_2.Module_4
    else if StateMachine.nextModeID == ModeID61AQ.ThreeModule_3 then mode_ThreeModule_3.Module_4
    else mode_FourModule_4.Module_4};

end StateMachine61AQ;
