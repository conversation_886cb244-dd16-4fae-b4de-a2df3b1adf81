within Workspace.Controller;
function Capacity_function_heating
  extends Modelica.Icons.Function;
  input Real OAT "Outside air temperature [K]";
  input Real Target_cap "Target capacity [W]";
  input Real Unit "Unit size identifier (40, 50, 60, 70)";
  output Real Target_Capacity "Final target capacity [W]";

  // Optimized variables with smooth transitions
  Real Max_Target_Cap40;
  Real Max_Target_Cap50;
  Real Max_Target_Cap60;
  Real Max_Target_Cap70;
  Real unit_weight[4] "Smooth unit selection weights";

  parameter Boolean is_ULN_option = false "ULN option flag";
  parameter Real transition_width = 2.0 "Temperature transition smoothing width [K]";
  parameter Real unit_transition_width = 5.0 "Unit selection smoothing width";

protected
  Real OAT_smooth_factor "Smooth transition factor for temperature";
  Real temp_threshold = 280.15 "Temperature threshold [K]";

algorithm
  // Smooth temperature transition using tanh function instead of sharp if-then-else
  OAT_smooth_factor := smooth(1, (Modelica.Math.tanh((OAT - temp_threshold)/transition_width) + 1)/2);

  // Optimized capacity calculations with smooth transitions
  Max_Target_Cap40 := smooth(1,
    if not is_ULN_option then
      38000 * (1 - OAT_smooth_factor) + (1356.2*OAT - 342613) * OAT_smooth_factor
    else
      31460 * (1 - OAT_smooth_factor) + (1356.2*OAT - 342613) * OAT_smooth_factor);

  Max_Target_Cap50 := smooth(1,
    if not is_ULN_option then
      48000 * (1 - OAT_smooth_factor) + (1660.7*OAT - 418425) * OAT_smooth_factor
    else
      38000 * (1 - OAT_smooth_factor) + (1660.7*OAT - 418425) * OAT_smooth_factor);

  Max_Target_Cap60 := smooth(1,
    if not is_ULN_option then
      57500 * (1 - OAT_smooth_factor) + (2154.6*OAT - 547745) * OAT_smooth_factor
    else
      46464 * (1 - OAT_smooth_factor) + (2154.6*OAT - 547745) * OAT_smooth_factor);

  Max_Target_Cap70 := 62000; // Constant value, no temperature dependency

  // Smooth unit selection using weighted interpolation instead of sharp if-elseif-else
  unit_weight[1] := smooth(1, (Modelica.Math.tanh((40 - abs(Unit - 40))/unit_transition_width) + 1)/2);
  unit_weight[2] := smooth(1, (Modelica.Math.tanh((50 - abs(Unit - 50))/unit_transition_width) + 1)/2);
  unit_weight[3] := smooth(1, (Modelica.Math.tanh((60 - abs(Unit - 60))/unit_transition_width) + 1)/2);
  unit_weight[4] := smooth(1, (Modelica.Math.tanh((70 - abs(Unit - 70))/unit_transition_width) + 1)/2);

  // Normalize weights to ensure they sum to 1
  unit_weight := unit_weight / sum(unit_weight);

  // Smooth capacity selection using weighted interpolation
  Target_Capacity := smooth(1, min(
    unit_weight[1] * Max_Target_Cap40 +
    unit_weight[2] * Max_Target_Cap50 +
    unit_weight[3] * Max_Target_Cap60 +
    unit_weight[4] * Max_Target_Cap70,
    Target_cap));

end Capacity_function_heating;
