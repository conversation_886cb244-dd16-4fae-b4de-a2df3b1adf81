within Workspace.Controller.Components.Functions;
function State_prediction
  extends Modelica.Icons.Function;
  input Real freq_compressor;
  input Integer N_module;
  input Real FreqTransition_1_2;
  input Real FreqTransition_2_3;
  input Real FreqTransition_3_4;
  input Real CapDesign_1;
  input Real CapDesign_2;
  input Real CapDesign_3;
  input Real CapDesign_4;
  input Real Target_cap;
  input Real Cap_1unit;
  input Boolean notMode1;
  input Real offset=15;
  output Integer State_id=1;
  Real ratio;
  Real next_Freq;
  // Real Freq_3units;
  // Real Freq_4units;
  Real[4] Capdesign={CapDesign_1,CapDesign_2,CapDesign_3,CapDesign_4};
  Real[3] FreqTransition={FreqTransition_1_2,FreqTransition_2_3,FreqTransition_3_4};
  Real currentCap=0;
  Real transiFreq;
algorithm
  ratio := Cap_1unit/CapDesign_1;

  // Optimized smooth state prediction to avoid Jacobian recalculations
  if notMode1 then
    State_id := 0;
  else
    // Use continuous approximation instead of discrete increments
    State_id := 1; // Base state
    for n in 1:N_module-1 loop
      currentCap := currentCap+Capdesign[n];
      transiFreq := FreqTransition[n];
      next_Freq := Target_cap*freq_compressor/ratio/(currentCap);

      // Smooth transition using tanh instead of sharp if-then
      State_id := State_id + (tanh(5*(next_Freq - transiFreq - offset)) + 1)/2;
    end for;

    // Round to nearest integer for final state
    State_id := integer(State_id + 0.5);
  end if;
end State_prediction;
