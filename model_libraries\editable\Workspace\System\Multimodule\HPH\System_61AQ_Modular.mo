within Workspace.System.Multimodule.HPH;
model System_61AQ_Modular
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=ECAT.CondBrineType_nd
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=ECAT.CondBrineConcentration_nd.setPoint
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    T_fixed=ECAT.CondBrineEWT_K.fixed and not ECAT.CondBrineFlowRate_m3s.fixed,
    Vd_set=ECAT.CondBrineFlowRate_m3s.setPoint,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-7.453968803000913,-7.453968803000905},{7.453968803000913,7.453968803000905}},origin={1.9999999999999991,-90.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    p_set=sourceBrine.p_set,
    X=BrineConcentration,
    Vd_fixed=false)
    annotation (Placement(transformation(extent={{-8.135620366158149,-8.135620366158022},{8.135620366158149,8.135620366158022}},origin={2.0,82.0},rotation=-90.0)));
  parameter.Modelica.SIunits.Temperature LWT=ECAT.CondBrineLWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=ECAT.CondBrineEWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=ECAT.HeatingAmbientAirDBTemp_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter Boolean use_bf=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=true
    annotation (Dialog(group="Use Parameters"));
  parameter Real Max_target_cap=Module_1.Module.Max_target_cap + Module_2.Module.Max_target_cap + Module_3.Module.Max_target_cap + Module_4.Module.Max_target_cap
    annotation (Dialog());
  inner.BOLT.GlobalParameters globalParameters(
    varLevel=.BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    oilMode=false)
    annotation (Placement(transformation(extent={{-53.21944393400101,82.57933406471223},{-35.50091896065509,100.29785903805815}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff_a=IsOFF1 and IsOFF2,
    isOff_b=IsOFF3 and IsOFF4,
    T_start=EWT,
    p_start=sourceBrine.p_set,
    mDot_a_start=Module_1.Module.mdot_start+Module_2.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF2)),
    mDot_b_start=Module_4.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF4))+Module_3.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF3)),
    fa_set=
      if(IsOFF3 and IsOFF4) then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.609284077593937,-2.6092840775939408},{2.609284077593937,2.6092840775939408}},origin={2.0,-64.0},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    fa_fixed=false,
    isOff_a=IsOFF1 and IsOFF2,
    isOff_b=IsOFF3 and IsOFF4,
    T_start=LWT,
    fa_set=
      if(IsOFF3 and IsOFF4) then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.609284077593937,-2.6092840775939408},{2.609284077593937,2.6092840775939408}},origin={2.0,42.82095294782522},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe external_system(
    Ka_fixed=true,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=LWT,
    isOff=false,
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{-3.899189164214886,-3.8991891642148317},{3.899189164214886,3.8991891642148317}},origin={2.0,65.1228975457677},rotation=90.0)));
  .BOLT.CoolantMisc.Split split2(
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=IsOFF1,
    isOff_b=IsOFF2,
    T_start=EWT,
    p_start=sourceBrine.p_set,
    mDot_a_start=Module_1.Module.mdot_start,
    mDot_b_start=Module_2.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF2)),
    fa_set=
      if IsOFF2 then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.6092840775939408,-2.6092840775939337},{2.6092840775939408,2.6092840775939337}},origin={-40.29048858852304,-44.90488588523063},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer2(
    fa_fixed=false,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=IsOFF1,
    isOff_b=IsOFF2,
    T_start=LWT,
    fa_set=
      if IsOFF2 then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.6092840775939408,-2.609284077593937},{2.6092840775939408,2.609284077593937}},origin={-37.83477006404283,26.956898078715273},rotation=90.0)));
  .BOLT.CoolantMisc.Split split3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff_a=IsOFF3,
    isOff_b=IsOFF4,
    p_start=sourceBrine.p_set,
    T_start=EWT,
    mDot_a_start=Module_3.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF3)),
    mDot_b_start=Module_4.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF4)),
    fa_set=
      if IsOFF4 then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.6092840775939337,-2.6092840775939337},{2.6092840775939337,2.6092840775939337}},origin={53.460312283756785,-47.202475482937636},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    fa_fixed=false,
    isOff_a=IsOFF3,
    isOff_b=IsOFF4,
    T_start=LWT,
    fa_set=
      if IsOFF4 then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.6092840775939337,-2.609284077593937},{2.6092840775939337,2.609284077593937}},origin={54.915401307241964,26.604772288074546},rotation=90.0)));
  .Workspace.System.Multimodule.HPH.Modular_61AQ Module_1(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF1,
    Selector_block_A=choiceBlock.Module_1.BlocA,
    is_monobloc=choiceBlock.Module_1.is_monobloc,
    Selector_block_B=choiceBlock.Module_1.BlocB,
    Module(
      CondFoulingFactor=ECAT.CondFoulingFactor_m2KW.setPoint,
      mdot_start=min(
        1/(1+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF4))*TargetCapacity,
        Module_1.Module.capacity_design[1])/(((((4180*(Module_1.Module.LWT-Module_1.Module.EWT))))))),
    SC_fixed={not ECAT.RefrigerantCharge_kg[1].fixed,not ECAT.RefrigerantCharge_kg[2].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[1].fixed,ECAT.RefrigerantCharge_kg[2].fixed},
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    LWT_fixed=ECAT.CondBrineLWT_K.fixed,
    EWT_fixed=ECAT.CondBrineEWT_K.fixed,
    FlowRate_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.CondBrineFlowRate_m3s.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    Relative_humidity=ECAT.HeatingAmbientAirRH_nd.setPoint,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    use_bf=use_bf,
    use_Calib=use_Calib,
    use_defrost=use_defrost,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_1.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_1.Coef_BufferTank,
    is_relative_humidity=ECAT.HeatingAmbientAirRH_nd.fixed,
    OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.setPoint,
    is_OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.fixed,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector,is_monobloc_db_initialization = false)
    annotation (Placement(transformation(extent={{-67.39379605212905,-15.910998912552001},{-53.80077542117267,-2.317978281595634}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.Multimodule.HPH.Modular_61AQ Module_2(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF2,
    Selector_block_A=choiceBlock.Module_2.BlocA,
    is_monobloc=choiceBlock.Module_2.is_monobloc,
    Selector_block_B=choiceBlock.Module_2.BlocB,
    SC_fixed={not ECAT.RefrigerantCharge_kg[3].fixed,not ECAT.RefrigerantCharge_kg[4].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[3].fixed,ECAT.RefrigerantCharge_kg[4].fixed},
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    BrineConcentration=BrineConcentration,
    controller_crkA(
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed),
    controller_crkB(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    LWT_fixed=ECAT.CondBrineLWT_K.fixed,
    EWT_fixed=ECAT.CondBrineEWT_K.fixed,
    Relative_humidity=ECAT.HeatingAmbientAirRH_nd.setPoint,
    FlowRate_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.CondBrineFlowRate_m3s.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    use_bf=use_bf,
    use_Calib=use_Calib,
    use_defrost=use_defrost,
    CoolantMedium=CoolantMedium,
    Module(
      mdot_start=min(
        1/(1+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF4))*TargetCapacity,
        Module_2.Module.capacity_design[1])/(((((4180*(Module_2.Module.LWT-Module_2.Module.EWT)))))),CondFoulingFactor = ECAT.CondFoulingFactor_m2KW.setPoint),
    is_monobloc_db_initialization=false,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_2.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_2.Coef_BufferTank,
    is_relative_humidity=ECAT.HeatingAmbientAirRH_nd.fixed,
    OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.setPoint,
    is_OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.fixed,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{-21.160635863603037,-16.66015823972354},{-7.840319384155951,-3.3398417602764603}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.Multimodule.HPH.Modular_61AQ Module_3(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF3,
    Selector_block_A=choiceBlock.Module_3.BlocA,
    is_monobloc=choiceBlock.Module_3.is_monobloc,
    Selector_block_B=choiceBlock.Module_3.BlocB,
    SC_fixed={not ECAT.RefrigerantCharge_kg[5].fixed,not ECAT.RefrigerantCharge_kg[6].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[5].fixed,ECAT.RefrigerantCharge_kg[6].fixed},
    BrineConcentration=BrineConcentration,
    CoolantMedium=CoolantMedium,
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed),
    LWT_fixed=ECAT.CondBrineLWT_K.fixed,
    EWT_fixed=ECAT.CondBrineEWT_K.fixed,
    Relative_humidity=ECAT.HeatingAmbientAirRH_nd.setPoint,
    FlowRate_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.CondBrineFlowRate_m3s.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    use_bf=use_bf,
    use_Calib=use_Calib,
    use_defrost=use_defrost,
    Module(
      mdot_start=min(
        1/(1+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF4))*TargetCapacity,
        Module_3.Module.capacity_design[1])/(((((4180*(Module_3.Module.LWT-Module_3.Module.EWT)))))),CondFoulingFactor = ECAT.CondFoulingFactor_m2KW.setPoint),
    is_monobloc_db_initialization=false,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_3.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_3.Coef_BufferTank,
    is_relative_humidity=ECAT.HeatingAmbientAirRH_nd.fixed,
    OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.setPoint,
    is_OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.fixed,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{26.171247801832557,-15.137341220496044},{40.44593024282465,-0.8626587795039562}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.Multimodule.HPH.Modular_61AQ Module_4(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF4,
    Selector_block_A=choiceBlock.Module_4.BlocA,
    is_monobloc=choiceBlock.Module_4.is_monobloc,
    Selector_block_B=choiceBlock.Module_4.BlocB,
    SC_fixed={not ECAT.RefrigerantCharge_kg[7].fixed,not ECAT.RefrigerantCharge_kg[8].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[7].fixed,ECAT.RefrigerantCharge_kg[8].fixed},
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed),
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    FlowRate=ECAT.CondBrineFlowRate_m3s.setPoint,
    FlowRate_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    Relative_humidity=ECAT.HeatingAmbientAirRH_nd.setPoint,
    EWT_fixed=ECAT.CondBrineEWT_K.fixed,
    LWT_fixed=ECAT.CondBrineLWT_K.fixed,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_bf=use_bf,
    use_en=Use_EN14511,
    use_Calib=use_Calib,
    use_defrost=use_defrost,
    Module(
      mdot_start=min(
        1/(1+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
          IsOFF4))*TargetCapacity,
        Module_4.Module.capacity_design[1])/(((((4180*(Module_4.Module.LWT-Module_4.Module.EWT)))))),CondFoulingFactor = ECAT.CondFoulingFactor_m2KW.setPoint),
    is_monobloc_db_initialization=false,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_4.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_3.Coef_BufferTank,
    is_relative_humidity=ECAT.HeatingAmbientAirRH_nd.fixed,
    OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.setPoint,
    is_OAT_WB=ECAT.HeatingAmbientAirWBTemp_K.fixed,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{72.01561485057283,-14.136109192975717},{85.2342523201471,-0.9174717234014471}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Power TargetCapacity=min(min(ECAT.TargetHeatingCapacity_W.setPoint,Max_target_cap),Max_max_target_cap_tot)
    annotation (Dialog(group="Cp_control"));
  .Modelica.Blocks.Sources.RealExpression controlCapacity(
    y=total_capacity)
    annotation (Placement(transformation(extent={{-134.0,-50.0},{-114.0,-30.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_out(
    T_start=LWT,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    p_set=Pdispo+sourceBrine.p_set,
    p_fixed=false)
    annotation (Placement(transformation(extent={{-1.8386468670603175,-1.8386468670603193},{1.8386468670603175,1.8386468670603193}},origin={2.1399275159560993,54.781667133009606},rotation=90.0)));
  parameter Boolean IsOFF1=false
    annotation (Dialog(tab="StateMachine"));
  parameter Boolean IsOFF2=false
    annotation (Dialog(tab="StateMachine"));
  parameter Boolean IsOFF3=true
    annotation (Dialog(tab="StateMachine"));
  parameter Boolean IsOFF4=true
    annotation (Dialog(tab="StateMachine"));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock_multi choiceBlock(
    Number_of_modules=4,
    Module_1_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_140,
    Module_2_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_100,
    Module_3_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_100,
    Module_4_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_100,
    Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD)
    annotation (Placement(transformation(extent={{-31.187096581335254,82.98944132491292},{-13.468571607989333,100.70796629825884}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.SIunits.Power total_inst_capacity=Module_1.Module.InstantaneousHeatingCapacity+Module_2.Module.InstantaneousHeatingCapacity+Module_3.Module.InstantaneousHeatingCapacity+Module_4.Module.InstantaneousHeatingCapacity
    annotation (Dialog(group="Cp_control"));
  .Workspace.Auxiliary.ECAT_Zenith.ECATBase ECAT(
    LoadRatio_nd(
      value=Module_1.controller_crkA.completeCompressorControl_base.capacity_controller.summary.AV*100,
      fixed=false),
    RefrigerantCharge_kg(
      value={Module_1.Module.BlockA.systemVariables.mRef[1],
            Module_1.Module.BlockB.systemVariables.mRef[1],
            Module_2.Module.BlockA.systemVariables.mRef[1],
            Module_2.Module.BlockB.systemVariables.mRef[1],
            Module_3.Module.BlockA.systemVariables.mRef[1],
            Module_3.Module.BlockB.systemVariables.mRef[1],
            Module_4.Module.BlockA.systemVariables.mRef[1],
            Module_4.Module.BlockB.systemVariables.mRef[1]}),
    EvapPumpSpeed_rpm(
      setPoint=1800),
    CondPumpSpeed_rpm(
      value=Module_1.Module.pumpPolyA.summary.speed,
      setPoint=1800),
    CondFoulingFactor_m2KW(
      setPoint=0),
    TargetHeatingCapacity_W(
      setPoint=300000),
    HeatingAmbientAirDBTemp_K(
      setPoint=273.15),
    CondBrineIntPressDrop_Pa(
      value=InternalPressureDrop_eq),
    CondPumpPower_W( value=Module_1.Module.pumpPolyA.summary.P_motor+Module_1.Module.pumpPolyB.summary.P_motor+Module_2.Module.pumpPolyA.summary.P_motor+Module_2.Module.pumpPolyB.summary.P_motor+Module_3.Module.pumpPolyA.summary.P_motor+Module_3.Module.pumpPolyB.summary.P_motor+Module_4.Module.pumpPolyA.summary.P_motor+Module_4.Module.pumpPolyB.summary.P_motor),
    PubHeatingCapacity_W(
      value=total_capacity),
    CondBrineFlowRate_m3s(
      setPoint=0.001,
      fixed=false,
      value=
        if ECAT.CondBrineFlowRate_m3s.fixed then
          sourceBrine.summary.Vd
        else
          CondFlowRate),
    CondBrineEWT_K(
      setPoint=303.15,
      value=
        if ECAT.CondBrineEWT_K.fixed then
          sourceBrine.summary.T
        else
          Cond_ewt),
    CondBrineLWT_K(
      setPoint=308.15,
      fixed=true,
      value=
        if ECAT.CondBrineLWT_K.fixed then
          sinkBrine.summary.T
        else
          Cond_lwt),
    PubUnitPower_W(
      value=total_power),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    TotalCompressorPower_W(
      value=sum(
        ECAT.CompressorPower_W.value)),
    TotalOilCharge_kg(
      value=Module_1.choiceBlock.Unit_Block_A.Oil_charge+Module_1.choiceBlock.Unit_Block_B.Oil_charge+Module_2.choiceBlock.Unit_Block_A.Oil_charge+Module_2.choiceBlock.Unit_Block_B.Oil_charge+Module_3.choiceBlock.Unit_Block_A.Oil_charge+Module_3.choiceBlock.Unit_Block_B.Oil_charge+Module_4.choiceBlock.Unit_Block_A.Oil_charge+Module_4.choiceBlock.Unit_Block_B.Oil_charge),
    TotalRefrigerantCharge_kg(
      value=Module_1.choiceBlock.Unit_Block_A.m_ref_chaud+Module_1.choiceBlock.Unit_Block_B.m_ref_chaud+Module_2.choiceBlock.Unit_Block_A.m_ref_chaud+Module_2.choiceBlock.Unit_Block_B.m_ref_chaud+Module_3.choiceBlock.Unit_Block_A.m_ref_chaud+Module_3.choiceBlock.Unit_Block_B.m_ref_chaud+Module_4.choiceBlock.Unit_Block_A.m_ref_chaud+Module_4.choiceBlock.Unit_Block_B.m_ref_chaud),
    EvapBrineFlowRate_m3s(
      value=-1),
    EvapBrineEWT_K(
      setPoint=285.15,
      value=-1),
    EvapBrineLWT_K(
      setPoint=280.15,
      value=-1),
    EvapFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineConcentration_nd(
      setPoint=0.4),
    AmbientAirDBTemp_K(
      setPoint=308.15),
    FanPower_W(
      value={Module_1.Module.BlockA.motor.summary.power_VFD,Module_1.Module.BlockB.motor.summary.power_VFD,Module_2.Module.BlockA.motor.summary.power_VFD,Module_2.Module.BlockB.motor.summary.power_VFD,Module_3.Module.BlockA.motor.summary.power_VFD,Module_3.Module.BlockB.motor.summary.power_VFD,Module_4.Module.BlockA.motor.summary.power_VFD,Module_4.Module.BlockB.motor.summary.power_VFD}),
    CompressorPower_W(
      value={Module_1.Module.BlockA.compressor.summary.P_compression,Module_1.Module.BlockB.compressor.summary.P_compression,Module_2.Module.BlockA.compressor.summary.P_compression,Module_2.Module.BlockB.compressor.summary.P_compression,Module_3.Module.BlockA.compressor.summary.P_compression,Module_3.Module.BlockB.compressor.summary.P_compression,Module_4.Module.BlockA.compressor.summary.P_compression,Module_4.Module.BlockB.compressor.summary.P_compression}),
    CompressorFrequency_Hz(
      value={Module_1.Module.BlockA.compressor.summary.Ncomp,Module_1.Module.BlockB.compressor.summary.Ncomp,Module_2.Module.BlockA.compressor.summary.Ncomp,Module_2.Module.BlockB.compressor.summary.Ncomp,Module_3.Module.BlockA.compressor.summary.Ncomp,Module_3.Module.BlockB.compressor.summary.Ncomp,Module_4.Module.BlockA.compressor.summary.Ncomp,Module_4.Module.BlockB.compressor.summary.Ncomp}),
    CondFanAirflowRate_m3s(
      value={2*Module_1.Module.BlockA.AirFlow,
             2*Module_1.Module.BlockB.AirFlow,
             2*Module_2.Module.BlockA.AirFlow,
             2*Module_2.Module.BlockB.AirFlow,
             2*Module_3.Module.BlockA.AirFlow,
             2*Module_3.Module.BlockB.AirFlow,
             2*Module_4.Module.BlockA.AirFlow,
             2*Module_4.Module.BlockB.AirFlow}),
    DischargeSuperheat_K(
      value={Module_1.Module.BlockA.node_discharge.dTsh,Module_1.Module.BlockB.node_discharge.dTsh,Module_2.Module.BlockA.node_discharge.dTsh,Module_2.Module.BlockB.node_discharge.dTsh,Module_3.Module.BlockA.node_discharge.dTsh,Module_3.Module.BlockB.node_discharge.dTsh,Module_4.Module.BlockA.node_discharge.dTsh,Module_4.Module.BlockB.node_discharge.dTsh}),
    CondSubcooling_K(
      value={Module_1.Module.BlockA.node_EXV_in.dTsh,Module_1.Module.BlockB.node_EXV_in.dTsh,Module_2.Module.BlockA.node_EXV_in.dTsh,Module_2.Module.BlockB.node_EXV_in.dTsh,Module_3.Module.BlockA.node_EXV_in.dTsh,Module_3.Module.BlockB.node_EXV_in.dTsh,Module_4.Module.BlockA.node_EXV_in.dTsh,Module_4.Module.BlockB.node_EXV_in.dTsh}),
    SuctionSuperheat_K(
      value={Module_1.Module.BlockA.node_suction.dTsh,Module_1.Module.BlockB.node_suction.dTsh,Module_2.Module.BlockA.node_suction.dTsh,Module_2.Module.BlockB.node_suction.dTsh,Module_3.Module.BlockA.node_suction.dTsh,Module_3.Module.BlockB.node_suction.dTsh,Module_4.Module.BlockA.node_suction.dTsh,Module_4.Module.BlockB.node_suction.dTsh}),
    RefrigerantDGT_K(
      value={Module_1.Module.BlockA.node_discharge.T,Module_1.Module.BlockB.node_discharge.T,Module_2.Module.BlockA.node_discharge.T,Module_2.Module.BlockB.node_discharge.T,Module_3.Module.BlockA.node_discharge.T,Module_3.Module.BlockB.node_discharge.T,Module_4.Module.BlockA.node_discharge.T,Module_4.Module.BlockB.node_discharge.T}),
    RefrigerantSCT_K(
      value={Module_1.Module.BlockA.node_condin.Tsat,Module_1.Module.BlockB.node_condin.Tsat,Module_2.Module.BlockA.node_condin.Tsat,Module_2.Module.BlockB.node_condin.Tsat,Module_3.Module.BlockA.node_condin.Tsat,Module_3.Module.BlockB.node_condin.Tsat,Module_4.Module.BlockA.node_condin.Tsat,Module_4.Module.BlockB.node_condin.Tsat}),
    RefrigerantSET_K(
      value={Module_1.Module.BlockA.node_evapout.Tsat,Module_1.Module.BlockB.node_evapout.Tsat,Module_2.Module.BlockA.node_evapout.Tsat,Module_2.Module.BlockB.node_evapout.Tsat,Module_3.Module.BlockA.node_evapout.Tsat,Module_3.Module.BlockB.node_evapout.Tsat,Module_4.Module.BlockA.node_evapout.Tsat,Module_4.Module.BlockB.node_evapout.Tsat}),
    RefrigerantSDT_K(
      value={Module_1.Module.BlockA.node_discharge.Tsat,Module_1.Module.BlockB.node_discharge.Tsat,Module_2.Module.BlockA.node_discharge.Tsat,Module_2.Module.BlockB.node_discharge.Tsat,Module_3.Module.BlockA.node_discharge.Tsat,Module_3.Module.BlockB.node_discharge.Tsat,Module_4.Module.BlockA.node_discharge.Tsat,Module_4.Module.BlockB.node_discharge.Tsat}),
    RefrigerantSST_K(
      value={Module_1.Module.BlockA.node_suction.Tsat,Module_1.Module.BlockB.node_suction.Tsat,Module_2.Module.BlockA.node_suction.Tsat,Module_2.Module.BlockB.node_suction.Tsat,Module_3.Module.BlockA.node_suction.Tsat,Module_3.Module.BlockB.node_suction.Tsat,Module_4.Module.BlockA.node_suction.Tsat,Module_4.Module.BlockB.node_suction.Tsat}),
    nbrCircuit=8,
    FanSpeed_rpm(
      value={Module_1.Module.BlockA.fanCurve.summary.speed,Module_1.Module.BlockB.fanCurve.summary.speed,Module_2.Module.BlockA.fanCurve.summary.speed,Module_2.Module.BlockB.fanCurve.summary.speed,Module_3.Module.BlockA.fanCurve.summary.speed,Module_3.Module.BlockB.fanCurve.summary.speed,Module_4.Module.BlockA.fanCurve.summary.speed,Module_4.Module.BlockB.fanCurve.summary.speed}),
    FanFrequency_Hz(
      value={Module_1.Module.BlockA.motor.summary.Motor_freq,Module_1.Module.BlockB.motor.summary.Motor_freq,Module_2.Module.BlockA.motor.summary.Motor_freq,Module_2.Module.BlockB.motor.summary.Motor_freq,Module_3.Module.BlockA.motor.summary.Motor_freq,Module_3.Module.BlockB.motor.summary.Motor_freq,Module_4.Module.BlockA.motor.summary.Motor_freq,Module_4.Module.BlockB.motor.summary.Motor_freq}),
    CondPumpSpeed_Hz(
      value={Module_1.Module.pumpPolyA.pump.speed_Hz,Module_1.Module.pumpPolyB.pump.speed_Hz,Module_2.Module.pumpPolyA.pump.speed_Hz,Module_2.Module.pumpPolyB.pump.speed_Hz,Module_3.Module.pumpPolyA.pump.speed_Hz,Module_3.Module.pumpPolyB.pump.speed_Hz,Module_4.Module.pumpPolyA.pump.speed_Hz,Module_4.Module.pumpPolyB.pump.speed_Hz}),
    CondPumpSpeed_rpm(
      value={Module_1.Module.pumpPolyA.summary.speed,Module_1.Module.pumpPolyB.summary.speed,Module_2.Module.pumpPolyA.summary.speed,Module_2.Module.pumpPolyB.summary.speed,Module_3.Module.pumpPolyA.summary.speed,Module_3.Module.pumpPolyB.summary.speed,Module_4.Module.pumpPolyA.summary.speed,Module_4.Module.pumpPolyB.summary.speed}),
    EvapPumpSpeed_Hz(
      value={Module_1.Module.pumpPolyA.pump.speed_Hz,Module_1.Module.pumpPolyB.pump.speed_Hz,Module_2.Module.pumpPolyA.pump.speed_Hz,Module_2.Module.pumpPolyB.pump.speed_Hz,Module_3.Module.pumpPolyA.pump.speed_Hz,Module_3.Module.pumpPolyB.pump.speed_Hz,Module_4.Module.pumpPolyA.pump.speed_Hz,Module_4.Module.pumpPolyB.pump.speed_Hz}),
    EvapPumpSpeed_rpm(
      value={Module_1.Module.pumpPolyA.summary.speed,Module_1.Module.pumpPolyB.summary.speed,Module_2.Module.pumpPolyA.summary.speed,Module_2.Module.pumpPolyB.summary.speed,Module_3.Module.pumpPolyA.summary.speed,Module_3.Module.pumpPolyB.summary.speed,Module_4.Module.pumpPolyA.summary.speed,Module_4.Module.pumpPolyB.summary.speed}),
    ElecFanBoxFrequency_Hz(
      value={Module_1.controllerSettings_crkA.FanSpeedBox,Module_1.controllerSettings_crkB.FanSpeedBox,Module_2.controllerSettings_crkA.FanSpeedBox,Module_2.controllerSettings_crkB.FanSpeedBox,Module_3.controllerSettings_crkA.FanSpeedBox,Module_3.controllerSettings_crkB.FanSpeedBox,Module_4.controllerSettings_crkA.FanSpeedBox,Module_4.controllerSettings_crkB.FanSpeedBox}),
    AmbientAirRH_nd(
      setPoint=0.6),
    CoolantFreezingTemp_K(
      value=Module_1.FreezTemp),
    EvapBrineIntPressDrop_Pa(
      value=InternalPressureDrop_eq),
    ExternalSystemPressureDrop_Pa(
      setPoint=80000,
      value=Module_1.AvailableStaticPressure),
    CondExternalSystemPressureDrop_Pa(
      value=Module_1.AvailableStaticPressure),
    PubHeatingCapacityInstantaneous_W(
      value=total_inst_capacity),
    HeatingAmbientAirRH_nd(
      value=Module_1.Module.BlockA.sourceAir.summary.RH,
      fixed=true,
      setPoint=0.87),
    HeatingAmbientAirWBTemp_K(
      value=Module_1.Module.BlockA.sourceAir.summary.Twb,
      fixed=false),IsMinimalCapacity(value = if Module_1.controller_crkA.completeCompressorControl_base.capacity_controller.summary.ID == -3 then 1 else 0))
    annotation (Placement(transformation(extent={{-76.02863704014574,80.26560887267502},{-56.97393282694172,99.32031308587904}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF1,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-2.832235297440448,-2.83223529744045},{2.832235297440448,2.83223529744045}},origin={-60.192850331125776,6.585965752142673},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF1,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-2.788390695504397,-2.788390695504397},{2.788390695504397,2.788390695504397}},origin={-60.81987828709961,-22.916542965079667},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_2(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF2,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-2.9911676372197604,-2.991167637219762},{2.9911676372197604,2.991167637219762}},origin={-14.434580424177838,7.754883001113264},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_2(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF2,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-3.1126415072158906,-3.1126415072158906},{3.1126415072158906,3.1126415072158906}},origin={-14.578148194047529,-22.916542965079667},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF3,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-2.936353796425884,-2.9363537964258857},{2.936353796425884,2.9363537964258857}},origin={32.84361430751739,8.547272256032485},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_4(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF3,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-2.723763419078921,-2.723763419078921},{2.723763419078921,2.723763419078921}},origin={33.2341588264354,-22.11708855179674},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF4,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-3.276575012193547,-3.276575012193552},{3.276575012193547,3.276575012193552}},origin={78.70982950569596,9.6726200833648},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_5(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff=IsOFF4,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-3.1198373374034816,-3.1198373374034816},{3.1198373374034816,3.1198373374034816}},origin={78.82779260081527,-21.643879009985323},rotation=90.0)));
  parameter.Modelica.SIunits.PressureDifference Pdispo=
    if choiceBlock.is_Pump then
      ECAT.ExternalSystemPressureDrop_Pa.setPoint
    else
      0
    annotation (Dialog(group="Cp_control"));
  parameter Boolean use_defrost=true
    annotation (Dialog(group="Use Parameters"));
  .Modelica.SIunits.PressureDifference InternalPressureDrop_eq=(Module_1.InternalPressureDrop*Inlet_.Vd+Module_2.InternalPressureDrop*Inlet_2.Vd+Module_3.InternalPressureDrop*Inlet_4.Vd+Module_4.InternalPressureDrop*Inlet_5.Vd)/node_out.Vd
    "Equivalent pressure drop for one unit instead of sevral parallele units.";
  replaceable.Workspace.Controller.SubSystems.PressureControl pressureControl(
    Av_min=0,
    Av_max=300,
    AV_start=1,
    Pressure_Gain=1/200000,
    Pressure_Setpoint=Pdispo+sourceBrine.p_set,
    LWT_Setpoint=LWT+0.001,
    LWT_Gain=1/(55-15),
    IsOff=not choiceBlock.is_Pump)
    constrainedby.Workspace.Controller.SubSystems.PressureControl
    annotation (Placement(transformation(extent={{-34.56477487656127,57.9798328584621},{-21.738984341664278,70.80562339335908}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.SIunits.Power total_power=Module_1.Module.controlledPower+Module_2.Module.controlledPower+Module_3.Module.controlledPower+Module_4.Module.controlledPower
    annotation (Dialog(group="Cp_control"));
  .Modelica.SIunits.Power total_capacity=Module_1.Module.controlledCapacity+Module_2.Module.controlledCapacity+Module_3.Module.controlledCapacity+Module_4.Module.controlledCapacity
    annotation (Dialog(group="Cp_control"));
  .Modelica.SIunits.VolumeFlowRate CondFlowRate=Module_1.Module.CondFlowRate+Module_2.Module.CondFlowRate+Module_3.Module.CondFlowRate+Module_4.Module.CondFlowRate
    "Condenser flowrate, Busines factor impact included";
  .Modelica.SIunits.Temperature Cond_ewt=(Module_1.Module.Cond_ewt*Module_1.Module.CondFlowRate+Module_2.Module.Cond_ewt*Module_2.Module.CondFlowRate+Module_3.Module.Cond_ewt*Module_3.Module.CondFlowRate+Module_4.Module.Cond_ewt*Module_4.Module.CondFlowRate)/(CondFlowRate)
    "Condenser flowrate, Busines factor impact included";
  .Modelica.SIunits.Temperature Cond_lwt=(Module_1.Module.Cond_lwt*Module_1.Module.CondFlowRate+Module_2.Module.Cond_lwt*Module_2.Module.CondFlowRate+Module_3.Module.Cond_lwt*Module_3.Module.CondFlowRate+Module_4.Module.Cond_lwt*Module_4.Module.CondFlowRate)/(CondFlowRate)
    "Condenser flowrate, Busines factor impact included";
    parameter Real Max_max_target_cap_tot = Module_1.Module.Max_max_target_cap_total+Module_2.Module.Max_max_target_cap_total+Module_3.Module.Max_max_target_cap_total+Module_4.Module.Max_max_target_cap_total;
equation
  connect(split.port_a,split2.port_c)
    annotation (Line(points={{0.6431722796511514,-61.39071592240606},{0.6431722796511514,-55.54360397213688},{-40.238302906971164,-55.54360397213688},{-40.238302906971164,-47.56635564437644}},color={0,127,0}));
  connect(mixer2.port_c,mixer.port_a)
    annotation (Line(points={{-37.83477006404283,29.670553519412973},{-37.83477006404283,35.685664994139216},{0.7475436427549076,35.685664994139216},{0.7475436427549076,40.211668870231286}},color={0,127,0}));
  connect(split.port_b,split3.port_c)
    annotation (Line(points={{3.35682772034885,-61.390715922406066},{3.35682772034885,-55.778439539120335},{53.51249796530866,-55.778439539120335},{53.51249796530866,-49.863945242083446}},color={0,127,0}));
  connect(mixer3.port_c,mixer.port_b)
    annotation (Line(points={{54.915401307241964,29.318427728772235},{54.915401307241964,36.068359992186316},{3.3568277203488486,36.068359992186316},{3.3568277203488486,40.21166887023128}},color={0,127,0}));
  connect(node_out.port_a,mixer.port_c)
    annotation (Line(points={{2.139927515956099,52.943020265949286},{2.139927515956099,45.53460838852291},{2.0000000000000004,45.53460838852291}},color={0,127,0}));
  connect(external_system.port_a,node_out.port_b)
    annotation (Line(points={{1.9999999999999991,61.22370838155281},{1.9999999999999991,56.620314000069925},{2.1399275159560998,56.620314000069925}},color={0,127,0}));
  connect(controlCapacity.y,Module_1.capacity_total)
    annotation (Line(points={{-113,-40},{-83.14843084893857,-40},{-83.14843084893857,-9.114488597073818},{-68.07344708367687,-9.114488597073818}},color={0,0,127}));
  connect(controlCapacity.y,Module_2.capacity_total)
    annotation (Line(points={{-113,-40},{-29.528887308016124,-40},{-29.528887308016124,-10},{-21.826651687575392,-10}},color={0,0,127}));
  connect(controlCapacity.y,Module_3.capacity_total)
    annotation (Line(points={{-113,-40},{19.44453949323625,-40},{19.44453949323625,-8},{25.457513679782952,-8}},color={0,0,127}));
  connect(controlCapacity.y,Module_4.capacity_total)
    annotation (Line(points={{-113,-40},{63.80433779079427,-40},{63.80433779079427,-7.526790458188582},{71.35468297709411,-7.526790458188582}},color={0,0,127}));
  connect(mixer2.port_a,Outlet_.port_b)
    annotation (Line(points={{-39.087226421287916,24.347614001121332},{-39.087226421287916,18.895026114534534},{-60.192850331125776,18.895026114534534},{-60.192850331125776,9.418201049583121}},color={0,127,0}));
  connect(Outlet_.port_a,Module_1.outlet)
    annotation (Line(points={{-60.192850331125776,3.753730454702225},{-60.192850331125776,-2.317978281595634},{-60.59728573665086,-2.317978281595634}},color={0,127,0}));
  connect(Module_1.inlet,Inlet_.port_b)
    annotation (Line(points={{-60.59728573665086,-15.910998912552001},{-60.81987828709961,-15.910998912552001},{-60.81987828709961,-20.12815226957527}},color={0,127,0}));
  connect(mixer2.port_b,Outlet_2.port_b)
    annotation (Line(points={{-36.47794234369398,24.347614001121332},{-36.47794234369398,19.068517547289563},{-14.434580424177838,19.068517547289563},{-14.434580424177838,10.746050638333024}},color={0,127,0}));
  connect(Outlet_2.port_a,Module_2.outlet)
    annotation (Line(points={{-14.434580424177838,4.763715363893502},{-14.500477623879494,4.763715363893502},{-14.500477623879494,-3.3398417602764594}},color={0,127,0}));
  connect(Module_2.inlet,Inlet_2.port_b)
    annotation (Line(points={{-14.500477623879494,-16.66015823972354},{-14.500477623879494,-19.803901457863777},{-14.578148194047529,-19.803901457863777}},color={0,127,0}));
  connect(Inlet_2.port_a,split2.port_b)
    annotation (Line(points={{-14.578148194047529,-26.029184472295558},{-14.578148194047529,-36},{-38.933660868174194,-36},{-38.933660868174194,-42.295601807636686}},color={0,127,0}));
  connect(split2.port_a,Inlet_.port_a)
    annotation (Line(points={{-41.647316308871886,-42.295601807636686},{-41.647316308871886,-36},{-60.81987828709961,-36},{-60.81987828709961,-25.704933660584064}},color={0,127,0}));
  connect(mixer3.port_a,Inlet_3.port_b)
    annotation (Line(points={{53.66294494999688,23.995488210480612},{53.66294494999688,19.78464219716754},{32.84361430751739,19.78464219716754},{32.84361430751739,11.48362605245837}},color={0,127,0}));
  connect(Inlet_3.port_a,Module_3.outlet)
    annotation (Line(points={{32.84361430751739,5.6109184596066015},{33.308589022328604,5.6109184596066015},{33.308589022328604,-0.8626587795039562}},color={0,127,0}));
  connect(Module_3.inlet,Inlet_4.port_b)
    annotation (Line(points={{33.308589022328604,-15.137341220496044},{33.308589022328604,-19.39332513271782},{33.2341588264354,-19.39332513271782}},color={0,127,0}));
  connect(Inlet_4.port_a,split3.port_a)
    annotation (Line(points={{33.2341588264354,-24.84085197087566},{33.2341588264354,-36.84415276005654},{52.10348456340794,-36.84415276005654},{52.10348456340794,-44.5931914053437}},color={0,127,0}));
  connect(mixer3.port_b,Outlet_3.port_b)
    annotation (Line(points={{56.27222902759081,23.995488210480612},{56.27222902759081,19.41848167427075},{78.70982950569596,19.41848167427075},{78.70982950569596,12.949195095558345}},color={0,127,0}));
  connect(Outlet_3.port_a,Module_4.outlet)
    annotation (Line(points={{78.70982950569596,6.396045071171254},{78.62493358535997,6.396045071171254},{78.62493358535997,-0.9174717234014471}},color={0,127,0}));
  connect(Module_4.inlet,Inlet_5.port_b)
    annotation (Line(points={{78.62493358535997,-14.136109192975717},{78.62493358535997,-18.524041672581838},{78.82779260081527,-18.524041672581838}},color={0,127,0}));
  connect(Inlet_5.port_a,split3.port_b)
    annotation (Line(points={{78.82779260081527,-24.763716347388808},{78.82779260081527,-36.84415276005654},{54.81714000410563,-36.84415276005654},{54.81714000410563,-44.5931914053437}},color={0,127,0}));
  connect(pressureControl.actuatorSignal,external_system.Ka_in)
    annotation (Line(points={{-21.09769481491943,64.3927281259106},{-11.677176496418728,64.3927281259106},{-11.677176496418728,63.71918944665034},{-1.7432215976462389,63.71918944665034}},color={0,0,127}));
  connect(pressureControl.measurementBus,Module_1.measurementBus)
    annotation (Line(points={{-34.56477487656127,68.24046528637969},{-67.31523125748139,68.24046528637969},{-67.31523125748139,-2.3478289010554025}},color={255,204,51}));
  connect(sourceBrine.port,split.port_c)
    annotation (Line(points={{2.000000000000001,-82.54603119699908},{2.000000000000001,-74.60375047807244},{2.052185681551878,-74.60375047807244},{2.052185681551878,-66.66146975914582}},color={0,127,0}));
  connect(sinkBrine.port,external_system.port_b)
    annotation (Line(points={{2.0000000000000018,73.86437963384185},{2.000000000000001,69.02208670998259}},color={0,127,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={229,152,23},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_61AQ_Modular;
